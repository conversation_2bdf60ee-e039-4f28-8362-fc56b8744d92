"""
Window Manager for Auto Shutdown Application
Handles creation and management of settings and shutdown overlay windows
"""

import os
import sys
import threading
import webview
import time
import subprocess
import json
import random
from pathlib import Path


class WindowManager:
    def __init__(self):
        """Initialize the window manager"""
        self.base_path = self._get_base_path()
        self.settings_window_active = False
        self.overlay_window_active = False

    def _get_base_path(self):
        """Get the correct base path for the application"""
        if getattr(sys, 'frozen', False):
            # Running from PyInstaller bundle
            if hasattr(sys, '_MEIPASS'):
                # onefile mode - static files are in temp directory
                return getattr(sys, '_MEIPASS', '')
            else:
                # onedir mode - static files are in the same directory as exe
                return os.path.dirname(sys.executable)
        else:
            # Running from source
            return os.path.dirname(os.path.abspath(__file__))

    def show_settings_window(self):
        """Show settings window using threading"""
        if self.settings_window_active:
            print("Settings window is already open")
            return

        def run_settings():
            try:
                from config_manager import ConfigManager

                self.settings_window_active = True

                class SettingsAPI:
                    def __init__(self):
                        self.config_manager = ConfigManager()
                        self.reload_signal_file = Path("scheduler_reload.signal")

                    def get_config(self):
                        """Get current configuration for web interface"""
                        config = self.config_manager.load_config()
                        print(f"API: Returning config: {config}")
                        return config

                    def save_config(self, new_config):
                        """Save configuration from web interface"""
                        success = self.config_manager.save_config(new_config)

                        if success:
                            # Create a signal file to notify main app to reload schedules
                            try:
                                self.reload_signal_file.write_text(str(time.time()))
                                print("Configuration saved and reload signal sent")
                            except Exception as e:
                                print(f"Failed to create reload signal: {e}")

                        return {"success": success}

                # Create API instance
                api = SettingsAPI()

                # Get the correct path to the HTML file
                html_path = os.path.join(self.base_path, 'static', 'settings.html')

                # Verify the HTML file exists
                if not os.path.exists(html_path):
                    print(f"HTML file not found: {html_path}")
                    return

                print(f"Loading settings window from: {html_path}")

                # Create settings window
                window = webview.create_window(
                    'Auto Shutdown Settings',
                    html_path,
                    width=800,
                    height=600,
                    resizable=True,
                    js_api=api
                )

                # Start webview
                webview.start()

            except Exception as e:
                print(f"Settings window error: {e}")
                import traceback
                traceback.print_exc()
            finally:
                self.settings_window_active = False

        # Run in separate thread
        thread = threading.Thread(target=run_settings, daemon=True)
        thread.start()

    def show_shutdown_overlay(self):
        """Show shutdown overlay window using threading"""
        if self.overlay_window_active:
            print("Shutdown overlay is already open")
            return

        def run_overlay():
            try:
                from config_manager import ConfigManager

                self.overlay_window_active = True

                class ShutdownOverlayAPI:
                    def __init__(self, base_path):
                        self.config_manager = ConfigManager()
                        self.base_path = base_path
                        config = self.config_manager.load_config()
                        self.countdown_duration = config.get('settings', {}).get('countdown_duration', 30)
                        self.countdown_thread = None
                        self.is_active = True
                        self.window_ready = False
                        self.sentences_data = []

                        # 加载句子数据
                        self.load_sentences()

                        print(f"Shutdown overlay initialized with {self.countdown_duration}s countdown")

                    def load_sentences(self):
                        """Load sentences from JSON file"""
                        try:
                            sentences_path = os.path.join(self.base_path, 'static', 'sentences.json')

                            if os.path.exists(sentences_path):
                                with open(sentences_path, 'r', encoding='utf-8') as f:
                                    self.sentences_data = json.load(f)
                                print(f"Loaded {len(self.sentences_data)} sentence collections")
                            else:
                                print(f"Sentences file not found: {sentences_path}")
                                self.sentences_data = []
                        except Exception as e:
                            print(f"Failed to load sentences: {e}")
                            self.sentences_data = []

                    def get_countdown_duration(self):
                        """Get countdown duration for JavaScript"""
                        return self.countdown_duration

                    def get_random_sentence(self):
                        """Get a random sentence for JavaScript"""
                        try:
                            # 如果没有加载到句子数据，使用内置的备用句子
                            if not self.sentences_data:
                                fallback_sentences = self.get_fallback_sentences()
                                random_collection = random.choice(fallback_sentences)
                                random_sentence = random.choice(random_collection['content'])
                                return {
                                    "content": random_sentence,
                                    "title": random_collection['title'],
                                    "author": random_collection['author']
                                }

                            # 随机选择一个文集
                            random_collection = random.choice(self.sentences_data)

                            # 随机选择该文集中的一句
                            random_sentence = random.choice(random_collection['content'])

                            return {
                                "content": random_sentence,
                                "title": random_collection['title'],
                                "author": random_collection['author']
                            }
                        except Exception as e:
                            print(f"Failed to get random sentence: {e}")
                            # 即使出错也返回随机的备用句子
                            fallback_sentences = self.get_fallback_sentences()
                            random_collection = random.choice(fallback_sentences)
                            random_sentence = random.choice(random_collection['content'])
                            return {
                                "content": random_sentence,
                                "title": random_collection['title'],
                                "author": random_collection['author']
                            }

                    def get_fallback_sentences(self):
                        """Get fallback sentences when main data fails to load"""
                        return [
                            {
                                "title": "默认句子",
                                "author": "系统",
                                "content": ["系统即将关机，请保存您的工作。"]
                            }
                        ]

                    def window_loaded(self):
                        """Called when the window is fully loaded and ready"""
                        print("Window loaded, starting countdown...")
                        self.window_ready = True

                        # 直接通过JavaScript设置句子
                        try:
                            sentence_data = self.get_random_sentence()
                            js_code = f"""
                            const sentenceContent = document.getElementById('sentence-content');
                            const sentenceMeta = document.getElementById('sentence-meta');
                            if (sentenceContent) {{
                                sentenceContent.textContent = {repr(sentence_data['content'])};
                            }}
                            if (sentenceMeta) {{
                                sentenceMeta.textContent = '—— {sentence_data["title"]} · {sentence_data["author"]}';
                            }}
                            """
                            webview.windows[0].evaluate_js(js_code)
                            print(f"Set sentence: {sentence_data['content'][:50]}...")
                        except Exception as e:
                            print(f"Failed to set sentence: {e}")

                        self.start_countdown()
                        return {"success": True}

                    def cancel_shutdown(self):
                        """Cancel shutdown (called from JavaScript)"""
                        print("Shutdown cancelled by user")
                        self.is_active = False
                        # Close the window
                        try:
                            webview.windows[0].destroy()
                        except:
                            pass
                        return {"success": True}

                    def start_countdown(self):
                        """Start the countdown timer"""
                        def countdown():
                            for remaining in range(self.countdown_duration, 0, -1):
                                if not self.is_active:
                                    return

                                # Update countdown display
                                try:
                                    webview.windows[0].evaluate_js(f'updateCountdown({remaining})')
                                except:
                                    pass

                                time.sleep(1)

                            # Time's up - execute shutdown
                            if self.is_active:
                                self.execute_shutdown()

                        self.countdown_thread = threading.Thread(target=countdown, daemon=True)
                        self.countdown_thread.start()

                    def execute_shutdown(self):
                        """Execute system shutdown"""
                        print("Executing shutdown...")
                        try:
                            if sys.platform == "win32":
                                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
                            elif sys.platform in ["linux", "darwin"]:
                                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
                            else:
                                print(f"Unsupported platform: {sys.platform}")
                        except subprocess.CalledProcessError as e:
                            print(f"Failed to shutdown: {e}")
                        except FileNotFoundError:
                            print("Shutdown command not found - running in test mode")

                # Create API instance
                api = ShutdownOverlayAPI(self.base_path)

                # Get the correct path to the HTML file
                html_path = os.path.join(self.base_path, 'static', 'shutdown_overlay.html')

                # Verify the HTML file exists
                if not os.path.exists(html_path):
                    print(f"HTML file not found: {html_path}")
                    return

                print(f"Loading shutdown overlay from: {html_path}")

                # Create fullscreen overlay window with optimized settings
                window = webview.create_window(
                    'Shutdown Warning',
                    html_path,
                    fullscreen=True,
                    on_top=True,
                    shadow=False,
                    resizable=False,
                    js_api=api,
                    minimized=False,
                    focus=True,
                    background_color='#1a1a2e'
                )

                # Start webview with optimized settings
                webview.start(
                    debug=False,
                    http_server=False,
                    private_mode=False
                )

            except Exception as e:
                print(f"Shutdown overlay error: {e}")
                import traceback
                traceback.print_exc()
            finally:
                self.overlay_window_active = False

        # Run in separate thread
        thread = threading.Thread(target=run_overlay, daemon=True)
        thread.start()

    def get_base_path(self):
        """Get the base path for the application"""
        return self.base_path
